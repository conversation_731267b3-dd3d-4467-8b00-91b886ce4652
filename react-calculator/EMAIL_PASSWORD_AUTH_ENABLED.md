# Email и Password авторизация включена

## Что было сделано

### 1. Включена авторизация по email и паролю
- Изменен параметр `isEmailPasswordAuthEnabled` с `false` на `true` в файле `config.json`
- Обновлена конфигурация для production и staging окружений

### 2. Файлы конфигурации
- **Production**: `/var/www/quer.us/current/public/terminal/config.json`
- **Staging**: `/var/www/s.quer.us/current/public/terminal/config.json`
- **Локальный**: `react-calculator/public/config.json`

### 3. Текущая конфигурация
```json
{
  "environment": "production",
  "target": "blue",
  "baseUrl": "https://quer.us",
  "apiEndpoints": {
    "go": "/api",
    "rust": "/api/calculate",
    "landing": "/api/landing"
  },
  "default": {
    "isEmailPasswordAuthEnabled": true
  },
  "versionRules": []
}
```

### 4. Доступные URL
- **Production**: https://quer.us/terminal/
- **Staging**: https://s.quer.us/terminal/
- **Контрольная панель**: https://quer.us/terminal/config-control.html

### 5. Как работает система
1. React приложение загружает конфигурацию из `config.json`
2. Параметр `isEmailPasswordAuthEnabled: true` включает формы email/password в Clerk
3. Пользователи теперь могут авторизоваться как через OAuth (Google, GitHub), так и через email/password

### 6. Управление конфигурацией
Для изменения настроек можно использовать утилиту `config-manager.sh`:

```bash
# Включить email/password для всех пользователей
./config-manager.sh default  # устанавливает isEmailPasswordAuthEnabled: false
./config-manager.sh moderation  # устанавливает isEmailPasswordAuthEnabled: true для версии 1.0.19

# Проверить текущую конфигурацию
./config-manager.sh check

# Показать версию приложения
./config-manager.sh version
```

### 7. Деплой изменений
После изменения конфигурации необходимо выполнить деплой:

```bash
# Production
./deploy-terminal-script.sh production

# Staging
./deploy-terminal-script.sh staging
```

## Статус
✅ **Авторизация по email и паролю включена для production и staging окружений**

Дата включения: 2025-06-22 17:30
